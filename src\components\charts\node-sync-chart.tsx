"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ChartDataItem,
  ChartTypeEnum,
  getTimeRanges,
  useChart,
} from "@/hooks/useChart";
import { Download, Info, ZoomIn, ZoomOut } from "lucide-react";
import { useMemo, useState } from "react";
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { downloadCSV, generateFilename, CHART_COLUMNS } from "@/utils/csv-export";

const CustomTooltip = ({ active, payload, label }: any) => {
  if (!active || !payload || !payload.length) return null;

  const date = new Date(label);
  const formattedDate = date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  const dataPoint = payload[0].payload;

  return (
    <div className="bg-background/95 border border-border rounded-md shadow-md p-3 text-sm">
      <p className="font-medium text-foreground mb-1">{formattedDate}</p>
      {payload.map((entry: any, index: number) => (
        <p key={index} style={{ color: entry.color }} className="text-xs">
          {entry.name}: {entry.value.toFixed(2)} GB
        </p>
      ))}
      {dataPoint.blockNumber && (
        <p className="text-xs mt-1">
          Block: {Number.parseInt(dataPoint.blockNumber).toLocaleString()}
        </p>
      )}
      {dataPoint.clientVersion && (
        <p className="text-xs">Client: {dataPoint.clientVersion}</p>
      )}
    </div>
  );
};

// Custom event marker component
const EventMarker = ({
  events,
  data,
  zoomLevel,
}: {
  events: ChartDataItem[] | undefined;
  data: any[];
  zoomLevel: number;
}) => {
  if (!data || data.length === 0) return null;

  return (
    <div className="absolute inset-0 pointer-events-none">
      {events?.map((event, index) => {
        // Find the data point closest to this event
        const eventDate = new Date(event.date).getTime();

        // Find the closest data point to this event
        let closestIndex = 0;
        let minDiff = Number.POSITIVE_INFINITY;

        data.forEach((item, idx) => {
          const itemDate = new Date(item.date).getTime();
          const diff = Math.abs(itemDate - eventDate);
          if (diff < minDiff) {
            minDiff = diff;
            closestIndex = idx;
          }
        });

        if (closestIndex === -1) return null;

        // Calculate position based on chart dimensions
        const x = (closestIndex / (data.length - 1)) * 100;
        const maxChainSize = Math.max(...data.map((item) => item.chainSize));
        const y =
          100 -
          (data[closestIndex].chainSize / (maxChainSize * zoomLevel)) * 100;

        return (
          <div
            key={index}
            className="absolute flex flex-col items-center"
            style={{
              left: `${x}%`,
              top: `${y}%`,
              transform: "translate(-50%, -100%)",
            }}
          >
            <div className="w-2 h-2 bg-primary rounded-full mb-1"></div>
            <div className="bg-card border border-border rounded p-2 text-xs max-w-[150px] shadow-md">
              <p className="font-bold">
                {new Date(event.date).toLocaleDateString()}
              </p>
              <p>{event.description}</p>
              {event.blockNumber && <p>Block: {event.blockNumber}</p>}
              {event.clientVersion && <p>Client: {event.clientVersion}</p>}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default function NodeSyncChart() {
  const [timeRange, setTimeRange] = useState<"1m" | "6m" | "1y" | "all">("all");
  const [zoomLevel, setZoomLevel] = useState(1);

  // Get time range based on selection
  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d");
      case "6m":
        return getTimeRanges("180d");
      case "1y":
        return getTimeRanges("1y");
      case "all":
        return getTimeRanges("all");
      default:
        return getTimeRanges("all");
    }
  }, [timeRange]);

  // Fetch node sync data from API
  const { data, isLoading, error } = useChart(
    startTime,
    endTime,
    ChartTypeEnum.FULL_NODE_SYNC as any,
  );

  console.log("data", data);

  // Format data for chart display
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data?.map((item) => {
      // Convert chainSize from bytes to GB
      const chainSizeGB =
        Number.parseFloat(item.chainSize) / (1024 * 1024 * 1024);

      return {
        date: new Date(item.timestamp).toISOString().split("T")[0],
        timestamp: item.timestamp,
        chainSize: chainSizeGB,
        blockNumber: item.blockNumber,
        clientVersion: item.clientVersion,
      };
    });
  }, [data]);

  // Handle CSV download
  const handleDownloadCSV = () => {
    if (!chartData.length) return;

    const columns = [
      ...CHART_COLUMNS.NODE_SYNC,
      { key: 'blockNumber', header: 'Block Number', formatter: (v: number) => v?.toLocaleString() || '' },
      { key: 'clientVersion', header: 'Client Version', formatter: (v: string) => v || '' },
    ] as const;

    downloadCSV({
      filename: generateFilename('node-sync', timeRange),
      data: chartData,
      columns,
    });
  };

  // Find events that should be displayed on the chart
  const visibleEvents = useMemo(() => {
    if (!chartData.length) return [];

    return data?.filter((event) => {
      const eventDate = new Date(event.date).getTime();
      const startDate = new Date(chartData[0].date).getTime();
      const endDate = new Date(chartData[chartData.length - 1].date).getTime();

      return eventDate >= startDate && eventDate <= endDate;
    });
  }, [chartData, data]);

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Helios Full Node Sync Chart</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Helios Full Node Sync (Default) Chart</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-1">
              <Button
                variant={timeRange === "1m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1m")}
              >
                1m
              </Button>
              <Button
                variant={timeRange === "6m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("6m")}
              >
                6m
              </Button>
              <Button
                variant={timeRange === "1y" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1y")}
              >
                1y
              </Button>
              <Button
                variant={timeRange === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("all")}
              >
                All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">Zoom:</span>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => setZoomLevel((prev) => Math.max(1, prev - 0.5))}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => setZoomLevel((prev) => Math.min(3, prev + 0.5))}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
            <div className="text-sm text-muted-foreground">
              Click and drag in the plot area to zoom in
            </div>
          </div>

          <div className="h-[500px] relative">
            {isLoading ? (
              <div className="w-full h-full flex items-center justify-center">
                <Skeleton className="w-full h-[450px]" />
              </div>
            ) : (
              <>
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={chartData}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) => {
                        const date = new Date(value);
                        return `${date.getMonth() + 1}/${date
                          .getFullYear()
                          .toString()
                          .slice(-2)}`;
                      }}
                    />
                    <YAxis
                      domain={[0, "dataMax"]}
                      label={{
                        value: "Chain data size (in GB)",
                        angle: -90,
                        position: "insideLeft",
                        style: { textAnchor: "middle" },
                      }}
                      tickFormatter={(value) => value.toFixed(2)}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area
                      type="monotone"
                      name="Chain Size"
                      dataKey="chainSize"
                      stroke="#3498db"
                      fill="#3498db"
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                  </AreaChart>
                </ResponsiveContainer>
                <EventMarker
                  events={visibleEvents}
                  data={chartData}
                  zoomLevel={zoomLevel}
                />
              </>
            )}
          </div>

          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleDownloadCSV}
              disabled={chartData.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Helios Full Node Sync (Default) Chart details the chain data size
            accompanied with block number and client version.
          </p>
          <div className="mt-4 space-y-4">
            <div className="bg-muted/50 p-4 rounded-md">
              <h3 className="text-sm font-medium mb-2">HIGHLIGHT</h3>
              <p className="text-sm text-muted-foreground">
                Helios Full Nodes can be synched using different state pruning
                modes. On [Default Settings] older states are pruned and with
                the Archive Settings all previous states are retained.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
