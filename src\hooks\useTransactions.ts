import apiClient from "@/lib/api/apiClient"
import type {
  InternalTransactionResponse,
  ListAccountCronsResponse,
  Transaction,
  TransactionByTxHashResponse,
  TransactionCountByAddressResponse,
  TransactionInternalFilter,
  TransactionListResponse,
  TransactionReceiptByTxHashResponse,
  TotalTransactionCountResponse,
  TransferTokensResponse,
} from "@/types/transactions"
import { useQuery, UseQueryOptions } from "@tanstack/react-query"
import { numberToHex } from "viem"

export interface TransactionListParams {
  page?: number
  limit?: number
  fromBlock?: number
  toBlock?: number
  fromAddress?: string
  toAddress?: string
  isPending?: boolean
  refetchInterval?: number
  cronId?: number | string
  hasValue?: boolean
}

export const useTransactionList = (
  params: TransactionListParams,
  options?: Omit<
    UseQueryOptions<TransactionListResponse | undefined, Error>,
    "queryKey" | "queryFn"
  >,
) => {
  const {
    page = 1,
    limit = 25,
    fromBlock,
    toBlock,
    fromAddress = "",
    toAddress = "",
    isPending,
    refetchInterval,
    cronId,
    hasValue,
  } = params

  return useQuery({
    queryKey: [
      "eth_listTransactions",
      page,
      limit,
      fromBlock,
      toBlock,
      fromAddress,
      toAddress,
      isPending,
      cronId,
      hasValue,
    ],
    queryFn: async () => {
      try {
        const response = await apiClient.post<{
          data: Transaction[]
          metadata: {
            total: number
            page: number
            limit: number
          }
        }>("api/", {
          jsonrpc: "2.0",
          method: "eth_listTransactions",
          params: [
            page,
            limit,
            {
              fromBlock,
              toBlock,
              fromAddress,
              toAddress,
              isPending,
              cronId,
              hasValue,
            },
          ],
          id: 1,
        })

        if (!response) return undefined

        return {
          id: response.id,
          jsonrpc: response.jsonrpc,
          result: response.result,
          mapNameTag: response.mapNameTag || {},
        }
      } catch (error) {
        console.error("Error fetching transactions:", error)
        throw error
      }
    },
    refetchInterval,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    ...options,
  })
}
export const useTransactionAndTransactionReceiptByTxHash = (txHash: string) => {
  return useQuery({
    queryKey: ["transactionAndTransactionReceiptByTxHash", txHash],
    queryFn: async () => {
      const response = await apiClient.post<
        TransactionByTxHashResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransactionByHash",
        params: [txHash],
        id: 1,
      })

      const responseReceipt = await apiClient.post<
        TransactionReceiptByTxHashResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransactionReceipt",
        params: [txHash],
        id: 1,
      })

      return {
        transaction: response?.result,
        receipt: responseReceipt?.result,
      }
    },
    staleTime: 5 * 60 * 1000,
    enabled: !!txHash,
  })
}

export const useTransactionCountByAddress = (address: string) => {
  return useQuery({
    queryKey: ["transactionCountByAddress", address],
    queryFn: async () => {
      const response = await apiClient.post<
        TransactionCountByAddressResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransactionCount",
        params: [address, "latest"],
        id: 1,
      })
      return response?.result
    },
    staleTime: 5 * 60 * 1000,
  })
}

export const usePendingTransactions = () => {
  return useQuery({
    queryKey: ["transactionCountByAddress"],
    queryFn: async () => {
      const response = await apiClient.post<
        TransactionListResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getPendingTransactions",
        id: 1,
      })
      return response?.result
    },
    staleTime: 5 * 60 * 1000,
  })
}

export const useGetAccountCrons = (
  address: string,
  page: number,
  limit: number,
) => {
  const isHex = (value: any) => /^0x[0-9a-fA-F]+$/.test(value)

  const pageHex = isHex(page) ? page : numberToHex(page)
  const limitHex = isHex(limit) ? limit : numberToHex(limit)

  return useQuery({
    queryKey: ["tokens", pageHex, limitHex],
    queryFn: async () => {
      const response = await apiClient.post<
        ListAccountCronsResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getAccountCronsByPageAndSize",
        params: [address, pageHex, limitHex],
        id: 1,
      })
      console.log("response", response)
      return response?.result
    },
  })
}

export const useInternalTransactionList = ({
  page = 1,
  limit = 10,
  filter,
}: {
  page?: number
  limit?: number
  filter?: TransactionInternalFilter
}) => {
  return useQuery({
    queryKey: ["eth_getListInternalTransactions", page, limit, filter],
    queryFn: async () => {
      const response = await apiClient.post<
        InternalTransactionResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getListInternalTransactions",
        params: [page, limit, filter],
        id: 1,
      })
      return response?.result
    },
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })
}

export const useGetTransferTokensByTransactionHash = (txHash: string) => {
  return useQuery({
    queryKey: ["transferTokensByTransactionHash", txHash],
    queryFn: async () => {
      const response = await apiClient.post<
        TransferTokensResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransferTokensByTransactionHash",
        params: [txHash],
        id: 1,
      })

      return response?.result
    },
    staleTime: 5 * 60 * 1000,
    enabled: !!txHash,
  })
}

export const useTotalTransaction = (refreshInterval?: number) => {
  return useQuery({
    queryKey: ["eth_getTransactionCount_total"],
    queryFn: async () => {
      const response = await apiClient.post<
        TotalTransactionCountResponse["result"] | undefined
      >("api/", {
        jsonrpc: "2.0",
        method: "eth_getTransactionCount",
        params: ["latest"],
        id: 1,
      })
      return response?.result
    },
    staleTime: 5 * 60 * 1000,
    refetchInterval: refreshInterval,
  })
}
