"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useMemo, useState } from "react";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { ChartType } from "@/hooks/useChart";
import { Download, Info } from "lucide-react";
import { downloadCSV, generateFilename, CHART_COLUMNS } from "@/utils/csv-export";

interface PriceDetailProps {
  chartType: ChartType;
  color: string;
}

// Mock price data since we don't have real price data in the API
const generatePriceData = (days: number) => {
  const data = [];
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  let price = 2000 + Math.random() * 200;

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);

    // Add some random price movement
    const change = (Math.random() - 0.5) * 50;
    price = Math.max(1800, Math.min(2500, price + change));

    data.push({
      date: date.toISOString().split("T")[0],
      timestamp: date.toISOString(),
      value: price,
      price: price,
      volume: Math.random() * 10000000000 + 5000000000,
      marketCap: price * 120000000, // Mock market cap calculation
    });
  }

  return data;
};

export default function PriceDetail({ chartType, color }: PriceDetailProps) {
  const [timeRange, setTimeRange] = useState<
    "24h" | "7d" | "1m" | "3m" | "1y" | "all"
  >("1m");

  // Generate mock price data based on selected time range
  const chartData = useMemo(() => {
    switch (timeRange) {
      case "24h":
        return generatePriceData(1);
      case "7d":
        return generatePriceData(7);
      case "1m":
        return generatePriceData(30);
      case "3m":
        return generatePriceData(90);
      case "1y":
        return generatePriceData(365);
      case "all":
        return generatePriceData(1000);
      default:
        return generatePriceData(30);
    }
  }, [timeRange]);

  // Get the latest price data
  const latestPrice = useMemo(() => {
    if (!chartData.length) return null;
    return chartData[chartData.length - 1];
  }, [chartData]);

  // Calculate price change
  const priceChange = useMemo(() => {
    if (!chartData.length || chartData.length < 2)
      return { value: 0, percentage: 0 };
    const currentPrice = chartData[chartData.length - 1].value;
    const previousPrice = chartData[0].value;
    const change = currentPrice - previousPrice;
    const percentage = (change / previousPrice) * 100;
    return { value: change, percentage };
  }, [chartData]);

  // Handle CSV download
  const handleDownloadCSV = () => {
    if (!chartData.length) return;

    downloadCSV({
      filename: generateFilename('ether-price', timeRange),
      data: chartData,
      columns: CHART_COLUMNS.PRICE,
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Ether Daily Price (USD) Chart</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-1">
              <Button
                variant={timeRange === "24h" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("24h")}
              >
                24h
              </Button>
              <Button
                variant={timeRange === "7d" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("7d")}
              >
                7d
              </Button>
              <Button
                variant={timeRange === "1m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1m")}
              >
                1m
              </Button>
              <Button
                variant={timeRange === "3m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("3m")}
              >
                3m
              </Button>
              <Button
                variant={timeRange === "1y" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1y")}
              >
                1y
              </Button>
              <Button
                variant={timeRange === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("all")}
              >
                All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <defs>
                  <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={color} stopOpacity={0} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => {
                    const date = new Date(value);
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }}
                />
                <YAxis
                  domain={["auto", "auto"]}
                  tickFormatter={(value) => `$${value.toLocaleString()}`}
                />
                <Tooltip
                  formatter={(value: any) => [
                    `$${value.toLocaleString()}`,
                    "Price",
                  ]}
                  labelFormatter={(label) =>
                    new Date(label).toLocaleDateString()
                  }
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke={color}
                  fillOpacity={1}
                  fill="url(#colorPrice)"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleDownloadCSV}
              disabled={chartData.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Price Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Current Price:</span>
                <span className="font-medium">
                  ${latestPrice?.value.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  Price Change ({timeRange}):
                </span>
                <span
                  className={`font-medium ${
                    priceChange.value >= 0 ? "text-green-500" : "text-red-500"
                  }`}
                >
                  {priceChange.value >= 0 ? "+" : ""}
                  {priceChange.value.toFixed(2)} (
                  {priceChange.percentage.toFixed(2)}%)
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  24h Low / 24h High:
                </span>
                <span className="font-medium">$1,980.25 / $2,045.12</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">7d Low / 7d High:</span>
                <span className="font-medium">$1,950.18 / $2,120.35</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  Trading Volume (24h):
                </span>
                <span className="font-medium">$15,234,567,890</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Market Cap Rank:</span>
                <span className="font-medium">#2</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Market Cap Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Market Cap:</span>
                <span className="font-medium">$242,916,285,018.00</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  Fully Diluted Valuation:
                </span>
                <span className="font-medium">$242,916,285,018.00</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  Circulating Supply:
                </span>
                <span className="font-medium">120,639,300.20 ETH</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total Supply:</span>
                <span className="font-medium">120,639,300.20 ETH</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Max Supply:</span>
                <span className="font-medium">∞</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Ether price chart shows the historical daily price of Ether
            (ETH) in USD. Ether is the native cryptocurrency of the Helios
            blockchain and is used to pay for transaction fees and computational
            services.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
