/**
 * Utility for exporting data to CSV format
 */

import { NATIVE_SYMBOL } from "@/constants";
import {
  formatEtherValue,
  formatWeiToEther,
} from "@/helpers/format";
import { detectMethod } from "@/helpers/transaction";

/**
 * Convert an array of objects to CSV format
 * @param data Array of objects to convert (only data currently displayed on screen)
 * @param headers Optional custom headers (if not provided, will use object keys)
 * @param filename Name of the file to download
 */
export function exportToCSV<T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[],
  filename = "export.csv",
): void {
  if (!data || !data.length) {
    console.warn("No data to export - no items currently displayed");
    return;
  }

  try {
    // If headers aren't provided, generate them from the first object's keys
    const csvHeaders =
      headers ||
      Object.keys(data[0]).map((key) => ({ key: key as keyof T, label: key }));

    // Create CSV header row
    const headerRow = csvHeaders.map((header) => `"${header.label}"`).join(",");

    // Create CSV data rows
    const csvRows = data.map((row) => {
      return csvHeaders
        .map((header) => {
          const value = row[header.key];
          // Handle different data types and ensure proper CSV formatting
          if (value === null || value === undefined) return '""';
          if (typeof value === "string")
            return `"${value.replace(/"/g, '""')}"`;
          if (typeof value === "object")
            return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
          return `"${value}"`;
        })
        .join(",");
    });

    // Combine header and data rows
    const csvString = [headerRow, ...csvRows].join("\n");

    // Create a blob and download link
    const blob = new Blob([csvString], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    // Set up and trigger download
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error exporting CSV:", error);
  }
}

/**
 * Format transaction data for CSV export (current page only)
 * @param transactions Array of transaction objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatTransactionsForCSV(transactions: any[]) {
  return transactions.map((tx) => {
    const method = detectMethod(tx);

    const formattedTransactionFee =
      tx?.gasPrice && tx?.cumulativeGasUsed
        ? formatEtherValue(
            (Number(tx.gasPrice) * Number(tx.cumulativeGasUsed)).toFixed(0),
          )
        : "N/A";

    return {
      hash: tx.hash,
      method: method.name,
      blockNumber: tx.blockNumber,
      timestamp: new Date(tx.createdAt).toISOString(),
      from: tx.from,
      to: tx.to,
      value: ` ${formatEtherValue(tx.value)} ${NATIVE_SYMBOL}`,
      transactionFee: formattedTransactionFee,
    };
  });
}

/**
 * Format token transfers for CSV export (current page only)
 * @param transfers Array of token transfer objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatTokenTransfersForCSV(transfers: any[]) {
  return transfers.map((transfer) => ({
    txHash: transfer.txHash,
    timestamp: new Date(transfer.timestamp).toISOString(),
    from: transfer.from,
    to: transfer.to,
    tokenAddress: transfer.tokenAddress,
    tokenName: transfer.tokenName,
    tokenSymbol: transfer.tokenSymbol,
    value: transfer.value,
    decimals: transfer.decimals,
  }));
}

/**
 * Format token transfer events for CSV export (current page only)
 * @param events Array of token transfer events currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatTokenTransferEventsForCSV(events: any[]) {
  return events.map((event) => ({
    transactionHash: event.transactionHash,
    blockNumber: event.blockNumber,
    timestamp: new Date(event.timestamp).toLocaleString(),
    from: event.from,
    to: event.to,
    amount: event.amount  || "0",
    tokenAddress: event.tokenAddress,
    tokenName: event.tokenDetails?.name || "Unknown",
    tokenSymbol: event.tokenDetails?.symbol || "Unknown",
    type: event.type,
    method: event.functionSignature,
  }));
}

/**
 * Format NFT transfers for CSV export (current page only)
 * @param transfers Array of NFT transfer objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatNFTTransfersForCSV(transfers: any[]) {
  return transfers.map((transfer) => ({
    txHash: transfer.txHash,
    timestamp: new Date(transfer.timestamp).toISOString(),
    from: transfer.from,
    to: transfer.to,
    tokenAddress: transfer.tokenAddress,
    tokenId: transfer.tokenId,
    tokenName: transfer.tokenName,
    tokenSymbol: transfer.tokenSymbol,
  }));
}

/**
 * Format blocks data for CSV export (current page only)
 * @param blocks Array of block objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatBlocksForCSV(blocks: any[]) {
  return blocks.map((block) => ({
    blockNumber: block.number,
    epochNumber: block.epochNumber,
    hash: block.hash,
    timestamp: new Date(block.timestamp).toISOString(),
    miner: block.miner,
    gasUsed: block.gasUsed,
    gasLimit: block.gasLimit,
    gasUsedPercentage: block.gasLimit ? ((Number(block.gasUsed) / Number(block.gasLimit)) * 100).toFixed(2) + "%" : "N/A",
    transactionCount: block.transactionCount,
    baseFee: block.baseFee ? formatEtherValue(block.baseFee) : "N/A",
    difficulty: block.difficulty,
    size: block.size,
  }));
}

/**
 * Format tokens list data for CSV export (current page only)
 * @param tokens Array of token objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatTokensForCSV(tokens: any[]) {
  return tokens.map((token, index) => {
    const metadata = token.metadata;
    const tokenAddress = metadata?.contract_address || metadata?.base;
    const formattedTotalSupply = token.total_supply && metadata?.decimals
      ? (Number(token.total_supply) / Math.pow(10, metadata.decimals)).toLocaleString(undefined, {
          maximumFractionDigits: 4,
        })
      : "-";

    return {
      rank: index + 1,
      address: tokenAddress || "N/A",
      name: metadata?.name || "Unknown",
      symbol: metadata?.symbol || "Unknown",
      decimals: metadata?.decimals || 0,
      totalSupply: formattedTotalSupply,
      holdersCount: token.holdersCount || 0,
      description: metadata?.description || "",
    };
  });
}

/**
 * Format token holders data for CSV export (current page only)
 * @param holders Array of token holder objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatTokenHoldersForCSV(holders: any[]) {
  return holders.map((holder, index) => ({
    rank: index + 1,
    address: holder.address,
    balance: holder.balance,
    balanceFormatted: holder.balanceUI || formatWeiToEther(holder.balance, holder.decimals || 18),
    percentage: holder.percentage ? holder.percentage.toFixed(4) + "%" : "N/A",
    transactionCount: holder.transactionCount || 0,
  }));
}

/**
 * Format epochs data for CSV export (current page only)
 * @param epochs Array of epoch objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatEpochsForCSV(epochs: any[]) {
  return epochs.map((epoch) => ({
    epochNumber: epoch.epochNumber,
    epochLength: epoch.epochLength,
    startBlock: epoch.startBlock,
    endBlock: epoch.endBlock,
    timestamp: new Date(epoch.timestamp).toISOString(),
    age: epoch.age,
  }));
}

/**
 * Format NFT collections data for CSV export (current page only)
 * @param collections Array of NFT collection objects currently displayed
 * @returns Formatted data ready for CSV export
 */
export function formatNFTCollectionsForCSV(collections: any[]) {
  return collections.map((collection, index) => ({
    rank: index + 1,
    address: collection.address,
    name: collection.name,
    symbol: collection.symbol,
    type: collection.type,
    createdAt: new Date(collection.createdAt).toISOString(),
    ownersCount: collection.ownersCount || 0,
    transfersCount: collection.transfersCount || 0,
    totalSupply: collection.totalSupply || 0,
  }));
}

/**
 * Format top holders data for CSV export (current page only)
 * @param holders Array of top holder objects currently displayed
 * @param startIndex Starting index for ranking
 * @returns Formatted data ready for CSV export
 */
export function formatTopHoldersForCSV(holders: any[], startIndex: number = 0) {
  return holders.map((holder, index) => ({
    rank: startIndex + index + 1,
    address: holder.address,
    balance: holder.balance,
    balanceFormatted: holder.balanceUI || formatWeiToEther(holder.balance, holder.decimals || 18),
    percentage: holder.percentage ? holder.percentage + "%" : "N/A",
    transactionCount: holder.txnCount || 0,
    tokenAddress: holder.tokenAddress || "N/A",
    "Date(UTC)": new Date().toISOString().split('T')[0],
    "UnixTimeStamp": Math.floor(Date.now() / 1000),
  }));
}
