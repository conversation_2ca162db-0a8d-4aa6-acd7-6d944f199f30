"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { formatNumberWithNotation } from "@/helpers/format"
import { ChartTypeEnum, getTimeRanges } from "@/hooks/useChart"
import { useTransactionList } from "@/hooks/useTransactions"
import { formatDate } from "date-fns"
import { Download, Info, Search } from "lucide-react"
import { useMemo, useState } from "react"
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import { downloadCSV, generateFilename } from "@/utils/csv-export"
import TimeRangeSelector, { TimeRangeOption } from "../time-range-seletor"
import UnifiedChartTooltip from "../unified-chart-tooltip"

const timeRangeOptions: TimeRangeOption[] = [
  { value: "1d", label: "1d" },
  { value: "1w", label: "1w" },
  { value: "1m", label: "1m" },
  { value: "3m", label: "3m" },
  { value: "all", label: "All" },
]

export default function PendingTransactionsChart() {
  const [timeRange, setTimeRange] = useState<string>("1m")
  const [searchDate, setSearchDate] = useState<string>("")

  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1d":
        return getTimeRanges("1d")
      case "1w":
        return getTimeRanges("7d")
      case "1m":
        return getTimeRanges("30d")
      case "3m":
        return getTimeRanges("90d")
      case "all":
        return getTimeRanges("all")
      default:
        return getTimeRanges("30d")
    }
  }, [timeRange])

  const { data: transactionData, isLoading } = useTransactionList({
    page: 1,
    limit: 100,
    isPending: true,
    refetchInterval: 30000,
  })

  // Process transaction data for the chart
  const chartData = useMemo(() => {
    if (
      !transactionData?.result?.data ||
      !Array.isArray(transactionData?.result.data)
    )
      return []

    // Group transactions by date and count them
    const groupedByDate = transactionData?.result.data.reduce((acc, tx) => {
      const date = new Date(tx?.timestamp).toISOString().split("T")[0]
      if (!acc[date]) {
        acc[date] = {
          date,
          value: 0,
          avgGasPrice: 0,
          totalGasPrice: 0,
          count: 0,
        }
      }
      acc[date].value += 1
      acc[date].totalGasPrice += Number.parseInt(tx?.gasPrice || "0", 16)
      acc[date].count += 1
      return acc
    }, {} as Record<string, any>)

    // Calculate averages and format for chart
    return Object.values(groupedByDate)
      .map((item: any) => ({
        ...item,
        avgGasPrice: item.count > 0 ? item.totalGasPrice / item.count / 1e9 : 0, // Convert to Gwei
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  }, [transactionData?.result])

  // Process transaction data for the table
  const tableData = useMemo(() => {
    if (
      !transactionData?.result?.data ||
      !Array.isArray(transactionData?.result.data)
    )
      return []

    // Group transactions by date and count them
    const groupedByDate = transactionData?.result.data.reduce((acc, tx) => {
      const date = new Date(tx?.timestamp).toISOString().split("T")[0]
      if (!acc[date]) {
        acc[date] = {
          date,
          count: 0,
        }
      }
      acc[date].count += 1
      return acc
    }, {} as Record<string, { date: string; count: number }>)

    // Format for table and sort by date (newest first)
    return Object.values(groupedByDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .filter((item) => {
        if (!searchDate) return true
        return item.date.includes(searchDate)
      })
  }, [transactionData?.result, searchDate])

  // Calculate total pending transactions
  const totalPendingTransactions = useMemo(() => {
    return transactionData?.result?.data?.length || 0
  }, [transactionData?.result])

  // Handle CSV download
  const handleDownloadCSV = () => {
    if (!chartData.length) return

    const columns = [
      { key: 'value', header: 'Pending Transactions', formatter: (v: number) => v.toLocaleString() },
      { key: 'avgGasPrice', header: 'Avg Gas Price (Gwei)', formatter: (v: number) => v.toFixed(2) },
      { key: 'count', header: 'Transaction Count', formatter: (v: number) => v.toLocaleString() },
    ]

    downloadCSV({
      filename: generateFilename('pending-transactions', timeRange),
      data: chartData.map(item => ({
        ...item,
        timestamp: new Date(item.date).toISOString(),
      })),
      columns,
    })
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card className="bg-card/50 h-full">
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center space-x-2">
                <CardTitle>Pending Transactions</CardTitle>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Info className="h-4 w-4" />
                </Button>
              </div>
              <TimeRangeSelector
                timeRange={timeRange}
                onChange={setTimeRange}
                options={timeRangeOptions}
              />
            </CardHeader>
            <CardContent>
              <div className="h-[500px]">
                {isLoading ? (
                  <div className="flex h-full w-full items-center justify-center">
                    <Skeleton className="h-[450px] w-full" />
                  </div>
                ) : (
                  <ChartContainer
                    config={{
                      pendingTransactions: {
                        label: "Pending Transactions",
                        color: "#3498db",
                      },
                    }}
                    className="h-full w-full"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={chartData}
                        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis
                          dataKey="date"
                          tickLine={false}
                          axisLine={false}
                          tickFormatter={(value) => {
                            const date = new Date(value)
                            return `${date.getMonth() + 1}/${date.getDate()}`
                          }}
                        />
                        <YAxis
                          tickLine={false}
                          axisLine={false}
                          tickFormatter={(value) => {
                            if (value >= 1000) {
                              return `${(value / 1000).toFixed(0)}k`
                            }
                            return value.toString()
                          }}
                          label={{
                            value: "Pending Transactions",
                            angle: -90,
                            position: "insideLeft",
                            style: { textAnchor: "middle" },
                          }}
                        />
                        <ChartTooltip
                          content={
                            <UnifiedChartTooltip
                              chartType={ChartTypeEnum.AVG_PENDING_TRANSACTION}
                            />
                          }
                          cursor={false}
                        />
                        <Area
                          type="monotone"
                          dataKey="value"
                          name="Pending Transactions"
                          stroke="#3498db"
                          fill="#3498db"
                          fillOpacity={0.2}
                          strokeWidth={2}
                          activeDot={{ r: 6, fill: "#3498db" }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                )}
              </div>
              <div className="mt-4 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                  onClick={handleDownloadCSV}
                  disabled={chartData.length === 0}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download CSV Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card className="bg-card/50 h-full">
            <CardHeader>
              <CardTitle>Pending Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="transactions" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="transactions">Transactions</TabsTrigger>
                  <TabsTrigger value="date">Date</TabsTrigger>
                </TabsList>
                <div className="my-4 relative">
                  <Input
                    placeholder="Search Date..."
                    value={searchDate}
                    onChange={(e) => setSearchDate(e.target.value)}
                    className="pr-10"
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
                <TabsContent value="transactions" className="mt-0">
                  <div className="rounded-md border max-h-[400px] overflow-y-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead className="text-right">
                            Transactions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoading ? (
                          Array.from({ length: 7 }).map((_, i) => (
                            <TableRow key={i}>
                              <TableCell>
                                <Skeleton className="h-4 w-24" />
                              </TableCell>
                              <TableCell className="text-right">
                                <Skeleton className="h-4 w-16 ml-auto" />
                              </TableCell>
                            </TableRow>
                          ))
                        ) : tableData.length > 0 ? (
                          tableData.map((row, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {formatDate(new Date(row.date), "dd MMM yyyy")}
                              </TableCell>
                              <TableCell className="text-right">
                                {formatNumberWithNotation(row.count)}
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell
                              colSpan={2}
                              className="text-center py-4 text-muted-foreground"
                            >
                              No transactions found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
                <TabsContent value="date" className="mt-0">
                  <div className="rounded-md border max-h-[400px] overflow-y-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead className="text-right">
                            Transactions
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {isLoading ? (
                          Array.from({ length: 7 }).map((_, i) => (
                            <TableRow key={i}>
                              <TableCell>
                                <Skeleton className="h-4 w-24" />
                              </TableCell>
                              <TableCell className="text-right">
                                <Skeleton className="h-4 w-16 ml-auto" />
                              </TableCell>
                            </TableRow>
                          ))
                        ) : tableData.length > 0 ? (
                          tableData.map((row, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {formatDate(new Date(row.date), "dd MMM yyyy")}
                              </TableCell>
                              <TableCell className="text-right">
                                {formatNumberWithNotation(row.count)}
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell
                              colSpan={2}
                              className="text-center py-4 text-muted-foreground"
                            >
                              No transactions found
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Daily Pending Transactions chart shows the average number of
            transactions waiting in the mempool to be included in a block. This
            metric provides insights into network congestion and transaction
            processing times.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Click and drag in the plot area to zoom in. You can also use the
            time range buttons to view different time periods.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
