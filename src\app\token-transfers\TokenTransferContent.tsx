"use client"

import Copy<PERSON>ooltip from "@/app/components/copy-tooltip"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

import { TokenLogo } from "@/components/nft-image"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatAge,
  formatHash,
  formatWeiToEther,
  normalizeAddress,
} from "@/helpers/format"
import { useGetTransferTokenEventByAddress } from "@/hooks/useTokens"
import { AlertCircle } from "lucide-react"

import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"

export default function TokenTransferContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const pageSize = Number(searchParams.get("pageSize") || "25")
  const limit = pageSize
  const addressFilter = searchParams.get("a")

  const {
    data: transfers,
    isLoading,
    isError,
    error,
  } = useGetTransferTokenEventByAddress(null, addressFilter, page, limit)

  const getTransactionDirection = (transfer: any) => {
    if (!addressFilter) return null
    const normalizedAddress = normalizeAddress(addressFilter)
    const normalizedTo = normalizeAddress(transfer?.to)

    if (normalizedAddress === normalizedTo) {
      return "in"
    }
    return "out"
  }



  const renderTokenLogo = (tokenDetails: any) => {
    return (
      <TokenLogo
        logoHash={tokenDetails?.logo}
        tokenName={tokenDetails?.name}
        size="sm"
      />
    )
  }

  const navigateToTransaction = (hash: string) => {
    router.push(PATH_ROUTER.TRANSACTION_DETAIL(hash))
  }

  const navigateToAddress = (address: string) => {
    router.push(
      PATH_ROUTER.ADDRESS_DETAIL(normalizeAddress(address) || address),
    )
  }

  const navigateToBlock = (blockNumber: number) => {
    router.push(PATH_ROUTER.BLOCK_DETAIL(blockNumber))
  }

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Token Transfers
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link href={`${PATH_ROUTER.TOKEN_TRANSFERS}?page=1`}>
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">
          Token Transfer Events
          {addressFilter && (
            <span className="text-lg font-normal text-muted-foreground ml-2">
              for {formatHash(normalizeAddress(addressFilter), 10, 9)}
            </span>
          )}
        </h1>
        {addressFilter && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(PATH_ROUTER.TOKEN_TRANSFERS)}
          >
            Clear Filter
          </Button>
        )}
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : !transfers?.data ||
        !Array.isArray(transfers.data) ||
        transfers.data.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-6 text-center">
          <h3 className="text-lg font-semibold">No Token Transfers Found</h3>
          <p className="text-sm text-muted-foreground mt-2">
            There are no token transfers available to display.
          </p>
        </div>
      ) : (
        <div className="card mb-4">
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]"></TableHead>
                  <TableHead>Transaction Hash</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Block</TableHead>
                  <TableHead>Age</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Token</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transfers.data.map((transfer, index) => {
                  const direction = getTransactionDirection(transfer)
                  const tokenDetails = transfer.tokenDetails
                  return (
                    <TableRow key={`${transfer?.transactionHash}-${index}`}>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          <div className="h-4 w-4 rounded-full border border-gray-300"></div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <CopyTooltip content={transfer?.transactionHash} />
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger
                                className="text-blue-700 hover:underline"
                                onClick={() =>
                                  navigateToTransaction(
                                    transfer?.transactionHash,
                                  )
                                }
                              >
                                {formatHash(transfer?.transactionHash, 6, 6)}
                              </TooltipTrigger>
                              <TooltipContent>
                                {transfer?.transactionHash}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-xs px-2 py-1 bg-gray-100 rounded-md inline-block">
                          {transfer?.functionSignature || "Transfer"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => navigateToBlock(transfer?.blockNumber)}
                        >
                          {transfer?.blockNumber}
                        </div>
                      </TableCell>
                      <TableCell>{formatAge(transfer?.timestamp)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger
                                className="text-blue-700 hover:underline"
                                onClick={() =>
                                  navigateToAddress(transfer?.from)
                                }
                              >
                                {formatHash(
                                  normalizeAddress(transfer?.from),
                                  6,
                                  4,
                                )}
                              </TooltipTrigger>
                              <TooltipContent>
                                {normalizeAddress(transfer?.from)}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <CopyTooltip
                            content={normalizeAddress(transfer?.from) ?? ""}
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {direction && (
                            <Badge
                              variant="outline"
                              className={`px-2 py-0.5 text-xs ${
                                direction === "in"
                                  ? "bg-green-50 text-green-700 border-green-200"
                                  : "bg-amber-50 text-amber-700 border-amber-200"
                              }`}
                            >
                              {direction === "in" ? "IN" : "OUT"}
                            </Badge>
                          )}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger
                                className="text-blue-700 hover:underline"
                                onClick={() => navigateToAddress(transfer?.to)}
                              >
                                {formatHash(
                                  normalizeAddress(transfer?.to),
                                  6,
                                  4,
                                )}
                              </TooltipTrigger>
                              <TooltipContent>
                                {normalizeAddress(transfer?.to)}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <CopyTooltip
                            content={normalizeAddress(transfer?.to) ?? ""}
                          />
                        </div>
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        {formatWeiToEther(transfer?.amount || transfer?.value)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {renderTokenLogo(tokenDetails)}
                          <div className="flex flex-col">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger
                                  className="text-blue-700 hover:underline text-left"
                                  onClick={() => {
                                    if (tokenDetails?.address) {
                                      router.push(
                                        PATH_ROUTER.TOKEN_DETAIL(
                                          tokenDetails.address,
                                        ),
                                      )
                                    }
                                  }}
                                >
                                  {tokenDetails?.name || "Unknown Token"}
                                </TooltipTrigger>
                                <TooltipContent>
                                  <div>
                                    <div>
                                      Token: {tokenDetails?.name || "Unknown"}
                                    </div>
                                    <div>
                                      Symbol: {tokenDetails?.symbol || "N/A"}
                                    </div>
                                    <div>
                                      Address: {tokenDetails?.address || "N/A"}
                                    </div>
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <span className="text-gray-500 text-xs">
                              ({tokenDetails?.symbol || "N/A"})
                            </span>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}

                {(!transfers?.data || transfers.data.length === 0) && (
                  <TableRow>
                    <TableCell
                      colSpan={9}
                      className="text-center py-6 text-muted-foreground"
                    >
                      No token transfers found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {!isLoading && transfers?.data && (
        <PaginationWithLinks
          page={page}
          pageSize={pageSize}
          totalCount={transfers?.metadata?.total || 0}
          pageSearchParam="page"
          pageSizeSelectOptions={{
            pageSizeOptions: [10, 25, 50, 100],
          }}
        />
      )}
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="card mb-4">
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>Transaction Hash</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Block</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>From</TableHead>
              <TableHead>To</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead>Token</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <div className="flex items-center justify-center">
                    <Skeleton className="h-4 w-4 rounded-full" />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-16" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-16" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-20" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-12" />
                    <Skeleton className="h-5 w-5" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-20 ml-auto" />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-5 rounded-full" />
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
