/**
 * CSV Export Utility for Chart Data
 * Provides standardized CSV export functionality for all chart types
 */

export interface CSVColumn {
  key: string;
  header: string;
  formatter?: (value: any) => string;
}

export interface CSVExportOptions {
  filename: string;
  data: any[];
  columns: readonly CSVColumn[];
  includeTimestamp?: boolean;
}

/**
 * Format timestamp to UTC date string
 */
export const formatDateUTC = (timestamp: string | number): string => {
  const date = new Date(timestamp);
  return date.toISOString().split('T')[0]; // YYYY-MM-DD format
};

/**
 * Get Unix timestamp from date string or timestamp
 */
export const getUnixTimestamp = (timestamp: string | number): number => {
  return Math.floor(new Date(timestamp).getTime() / 1000);
};

/**
 * Escape CSV field if it contains special characters
 */
const escapeCSVField = (field: string): string => {
  if (field.includes(',') || field.includes('"') || field.includes('\n')) {
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
};

/**
 * Generate CSV content from data and columns
 */
export const generateCSVContent = (options: CSVExportOptions): string => {
  const { data, columns, includeTimestamp = true } = options;
  
  if (!data || data.length === 0) {
    return '';
  }

  // Build headers - always include Date(UTC) and UnixTimeStamp first
  const headers: string[] = [];
  if (includeTimestamp) {
    headers.push('Date(UTC)', 'UnixTimeStamp');
  }
  headers.push(...columns.map(col => col.header));

  // Build rows
  const rows = data.map(item => {
    const row: string[] = [];
    
    // Add timestamp columns if enabled
    if (includeTimestamp) {
      const timestamp = item.timestamp || item.date;
      if (timestamp) {
        row.push(formatDateUTC(timestamp));
        row.push(getUnixTimestamp(timestamp).toString());
      } else {
        row.push('', '');
      }
    }
    
    // Add data columns
    columns.forEach(column => {
      let value = item[column.key];
      
      // Apply formatter if provided
      if (column.formatter && value !== undefined && value !== null) {
        value = column.formatter(value);
      } else if (value === undefined || value === null) {
        value = '';
      } else {
        value = value.toString();
      }
      
      row.push(escapeCSVField(value));
    });
    
    return row.join(',');
  });

  return [headers.join(','), ...rows].join('\n');
};

/**
 * Download CSV file
 */
export const downloadCSV = (options: CSVExportOptions): void => {
  const csvContent = generateCSVContent(options);
  
  if (!csvContent) {
    console.warn('No data to export');
    return;
  }

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  
  link.setAttribute('href', url);
  link.setAttribute('download', options.filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
};

/**
 * Generate filename with timestamp
 */
export const generateFilename = (baseName: string, timeRange?: string): string => {
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  const parts = [baseName];
  
  if (timeRange) {
    parts.push(timeRange);
  }
  
  parts.push(timestamp);
  
  return `${parts.join('-')}.csv`;
};

/**
 * Common column definitions for different chart types
 */
export const CHART_COLUMNS = {
  // Transaction-related charts
  TRANSACTIONS: [
    { key: 'value', header: 'Transaction Count', formatter: (v: number) => v.toLocaleString() },
    { key: 'transactionCount', header: 'Transaction Count', formatter: (v: number) => v.toLocaleString() },
  ],
  
  // Address-related charts  
  ACTIVE_ADDRESS: [
    { key: 'addressActive', header: 'Active Addresses', formatter: (v: number) => v.toLocaleString() },
    { key: 'addressSend', header: 'Sending Addresses', formatter: (v: number) => v.toLocaleString() },
    { key: 'addressReceive', header: 'Receiving Addresses', formatter: (v: number) => v.toLocaleString() },
  ],
  
  // ERC20 Address charts
  ERC20_ADDRESS: [
    { key: 'erc20AddressActive', header: 'Active ERC20 Addresses', formatter: (v: number) => v.toLocaleString() },
    { key: 'erc20AddressSend', header: 'ERC20 Sending Addresses', formatter: (v: number) => v.toLocaleString() },
    { key: 'erc20AddressReceive', header: 'ERC20 Receiving Addresses', formatter: (v: number) => v.toLocaleString() },
  ],
  
  // Block-related charts
  BLOCKS: [
    { key: 'blockCount', header: 'Block Count', formatter: (v: number) => v.toLocaleString() },
    { key: 'avgBlockTime', header: 'Avg Block Time (s)', formatter: (v: number) => v.toFixed(2) },
    { key: 'avgBlockSize', header: 'Avg Block Size', formatter: (v: string) => v },
  ],
  
  // Contract-related charts
  CONTRACTS: [
    { key: 'value', header: 'Contract Count', formatter: (v: number) => v.toLocaleString() },
    { key: 'totalDeployed', header: 'Total Deployed', formatter: (v: number) => v.toLocaleString() },
    { key: 'deploymentGas', header: 'Avg Deployment Gas', formatter: (v: number) => v.toLocaleString() },
  ],
  
  // Node sync chart
  NODE_SYNC: [
    { key: 'chainSize', header: 'Chain Size (GB)', formatter: (v: number) => v.toFixed(2) },
  ],
  
  // Price charts
  PRICE: [
    { key: 'price', header: 'Price (USD)', formatter: (v: number) => `$${v.toFixed(4)}` },
    { key: 'volume', header: 'Volume', formatter: (v: number) => v.toLocaleString() },
    { key: 'marketCap', header: 'Market Cap', formatter: (v: number) => `$${v.toLocaleString()}` },
  ],
  
  // Fee charts
  FEES: [
    { key: 'avgFee', header: 'Average Fee (ETH)', formatter: (v: number) => v.toFixed(8) },
    { key: 'totalFees', header: 'Total Fees (ETH)', formatter: (v: number) => v.toFixed(4) },
    { key: 'medianFee', header: 'Median Fee (ETH)', formatter: (v: number) => v.toFixed(8) },
  ],
  
  // Burn charts
  BURN: [
    { key: 'burntFees', header: 'Burnt Fees (ETH)', formatter: (v: number) => v.toFixed(4) },
    { key: 'burntFeesUsd', header: 'Burnt Fees (USD)', formatter: (v: number) => `$${v.toFixed(2)}` },
  ],
} as const;
