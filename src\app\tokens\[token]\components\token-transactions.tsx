"use client"

import { CSVExportButton } from "@/components/csv-button-export"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { PATH_ROUTER } from "@/constants/routers"
import {
  formatHash,
  formatTimestamp,
  formatWeiToEther,
  normalizeAddress,
} from "@/helpers/format"
import { useGetTransferTokenEventByAddress } from "@/hooks/useTokens"
import { formatTokenTransferEventsForCSV } from "@/lib/utils/csv-export"
import type { TokenResult, TransferTokenEvent } from "@/types/tokens"
import { TokenType } from "@/types/tokens" // Import TokenType
import { AlertCircle, ArrowRight, Download, Filter } from "lucide-react"
import { NFTImage } from "@/components/nft-image"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { numericFormatter } from "react-number-format"

interface TokenTransactionsProps {
  tokenAddress: string
  tokenDetail: TokenResult | undefined
}

export function TokenTransactions({
  tokenAddress,
  tokenDetail,
}: TokenTransactionsProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Math.max(1, Number(searchParams.get("page")) || 1)
  const limit = 25

  const {
    data: transactionData,
    isLoading,
    isError,
    error,
  } = useGetTransferTokenEventByAddress(tokenAddress, null, page, limit)

  const transactions: TransferTokenEvent[] = transactionData?.data || []
  const totalTransactions = transactionData?.metadata?.total || 0

  // Determine if this is an NFT token
  const isNftToken =
    tokenDetail?.type === TokenType.ERC721 ||
    tokenDetail?.type === TokenType.ERC1155

  const handleNftItemClick = (tx: TransferTokenEvent) => {
    if (isNftToken && tx.tokenId) {
      router.push(`/nft/${tokenAddress}/${tx.tokenId}`)
    }
  }

  if (isError) {
    return (
      <Card className="w-full">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-10 w-10 text-red-500 mb-4" />
            <h3 className="text-lg font-semibold">
              Error Loading Token Transactions
            </h3>
            <p className="text-sm text-muted-foreground mt-2">
              {error instanceof Error
                ? error.message
                : "An unexpected error occurred"}
            </p>
            <Button asChild className="mt-4">
              <Link
                href={`${PATH_ROUTER.TOKEN_DETAIL(
                  tokenAddress,
                )}?tab=transactions&page=1`}
              >
                Try Again
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          {!isLoading ? (
            <>
              {numericFormatter(`${totalTransactions}`, {
                thousandSeparator: true,
                decimalScale: 0,
              })}{" "}
              {totalTransactions === 1 ? 'transaction' : 'transactions'} found
            </>
          ) : (
            <Skeleton className="h-5 w-40" />
          )}
        </div>
        <div className="flex gap-2">
          <CSVExportButton
            data={transactions}
            formatter={formatTokenTransferEventsForCSV}
            filename={`token-${tokenAddress}-transactions-page-${page}.csv`}
            disabled={isLoading || transactions.length === 0}
          />
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      <div className="card">
        {isLoading ? (
          <div className="rounded-md border">
            <LoadingTransactionsTable isNft={isNftToken} />
          </div>
        ) : transactions.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg">
            <h3 className="text-lg font-semibold">No Transactions Found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              There are no token transactions available to display.
            </p>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]"></TableHead>
                  <TableHead>Transaction Hash</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Block</TableHead>
                  <TableHead>Age</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                  {isNftToken ? (
                    <TableHead>Item</TableHead>
                  ) : (
                    <TableHead className="text-right">Amount</TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((tx) => (
                  <TableRow key={tx.id}>
                    <TableCell>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </TableCell>
                    <TableCell
                      className="font-mono text-sm cursor-pointer"
                      onClick={() => {
                        router.push(
                          `${PATH_ROUTER.TRANSACTION_DETAIL(
                            tx.transactionHash,
                          )}`,
                        )
                      }}
                    >
                      <span className="text-blue-500">
                        {tx.transactionHash.slice(0, 10)}...
                        {tx.transactionHash.slice(-8)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {tx.functionSignature ||
                          (isNftToken ? "Transfer" : "Transfer")}
                      </span>
                    </TableCell>
                    <TableCell
                      className="cursor-pointer"
                      onClick={() => {
                        router.push(
                          `${PATH_ROUTER.BLOCK_DETAIL(tx.blockNumber)}`,
                        )
                      }}
                    >
                      <span className="text-blue-500">{tx.blockNumber}</span>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger>
                            {formatTimestamp(tx?.timestamp)}
                          </TooltipTrigger>
                          <TooltipContent>
                            {new Date(tx?.timestamp).toLocaleString()}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell
                      className="font-mono text-sm cursor-pointer"
                      onClick={() => {
                        router.push(`${PATH_ROUTER.ADDRESS_DETAIL(tx.from)}`)
                      }}
                    >
                      <span className="text-blue-500">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger className="text-blue-700 hover:underline">
                              {formatHash(normalizeAddress(tx.from), 10, 9)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.from)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </span>
                    </TableCell>
                    <TableCell
                      className="font-mono text-sm cursor-pointer"
                      onClick={() => {
                        router.push(PATH_ROUTER.ADDRESS_DETAIL(tx.to))
                      }}
                    >
                      <span className="text-blue-500">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger className="text-blue-700 hover:underline">
                              {formatHash(normalizeAddress(tx.to), 10, 9)}
                            </TooltipTrigger>
                            <TooltipContent>
                              {normalizeAddress(tx.to)}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </span>
                    </TableCell>
                    {isNftToken ? (
                      <TableCell>
                        <div
                          className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                          onClick={() => handleNftItemClick(tx)}
                        >
                          <NFTImage
                            src={tx.nftDetails?.image}
                            alt={tx.nftDetails?.name || `Token #${tx.tokenId}`}
                            size="sm"
                            rounded={false}
                          />
                          <div className="flex flex-col">
                            <span className="text-sm font-medium text-blue-600 hover:underline">
                              {tx.nftDetails?.name ||
                                tokenDetail?.name ||
                                "Unknown"}
                            </span>
                            {tx.tokenId && (
                              <span className="text-xs text-gray-500">
                                #{tx.tokenId}
                              </span>
                            )}
                          </div>
                        </div>
                      </TableCell>
                    ) : (
                      <TableCell className="text-right font-mono text-sm">
                        {tokenDetail
                          ? formatWeiToEther(
                              tx.amount || tx.value,
                              tokenDetail.decimals,
                            )
                          : formatWeiToEther(tx.amount || tx.value)}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
      {!isLoading && (
        <PaginationWithLinks
          page={page}
          pageSize={limit}
          totalCount={totalTransactions}
          baseUrl={`${PATH_ROUTER.TOKEN_DETAIL(tokenAddress)}?tab=transactions`}
        />
      )}
    </div>
  )
}

function LoadingTransactionsTable({ isNft }: { isNft: boolean }) {
  return (
    <div className="w-full overflow-auto">
      <table className="w-full caption-bottom text-sm">
        <thead className="[&_tr]:border-b">
          <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground w-[50px]"></th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Transaction Hash
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Method
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Block
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              Age
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              From
            </th>
            <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
              To
            </th>
            <th
              className={`h-12 px-4 align-middle font-medium text-muted-foreground ${
                isNft ? "text-left" : "text-right"
              }`}
            >
              {isNft ? "Item" : "Amount"}
            </th>
          </tr>
        </thead>
        <tbody className="[&_tr:last-child]:border-0">
          {Array.from({ length: 10 }).map((_, index) => (
            <tr
              key={index}
              className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
            >
              <td className="p-4 align-middle">
                <Skeleton className="h-8 w-8 rounded-full" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-32" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-6 w-16 rounded-full" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-16" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-24" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-28" />
              </td>
              <td className="p-4 align-middle">
                <Skeleton className="h-5 w-28" />
              </td>
              <td className="p-4 align-middle">
                {isNft ? (
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-8 w-8 rounded" />
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  </div>
                ) : (
                  <div className="text-right">
                    <Skeleton className="h-5 w-20 ml-auto" />
                  </div>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}
