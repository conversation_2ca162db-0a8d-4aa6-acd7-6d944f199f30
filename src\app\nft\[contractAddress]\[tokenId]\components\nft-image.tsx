"use client"

import { Card, CardContent } from "@/components/ui/card"
import { NFTImage as BaseNFTImage } from "@/components/nft-image"

interface NFTImageProps {
  image?: string
  name?: string
  tokenId: string
}

export function NFTImage({ image, name, tokenId }: NFTImageProps) {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="aspect-square relative bg-gray-100 flex items-center justify-center">
          <BaseNFTImage
            src={image}
            alt={name || `Token #${tokenId}`}
            className="!h-full !w-full !rounded-none !border-0"
            size="2xl"
            rounded={false}
          />
        </div>
      </CardContent>
    </Card>
  )
}
