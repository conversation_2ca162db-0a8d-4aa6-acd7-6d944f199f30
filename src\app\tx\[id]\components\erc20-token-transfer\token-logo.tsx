"use client"

import { Token<PERSON>ogo as BaseTokenLogo } from "@/components/nft-image"
import { TokenType } from "@/types/tokens"
import { Coins, ImageIcon } from "lucide-react"

interface TokenDetails {
  address: string
  name: string
  symbol: string
  logo: string | null
  decimals?: number
}

interface TokenLogoProps {
  tokenDetails: TokenDetails | null | undefined
  tokenType: string
  size?: number
}

export function TokenLogo({
  tokenDetails,
  tokenType,
  size = 20,
}: TokenLogoProps) {
  // For NFT tokens without logo, show appropriate icon
  if (!tokenDetails?.logo) {
    if (tokenType === TokenType.ERC721 || tokenType === TokenType.ERC1155) {
      return <ImageIcon className="h-5 w-5 text-purple-500" />
    }
    return <Coins className="h-5 w-5 text-blue-500" />
  }

  return (
    <BaseTokenLogo
      logoHash={tokenDetails.logo}
      tokenName={tokenDetails.name}
      size="sm"
      className={`!h-[${size}px] !w-[${size}px]`}
    />
  )
}
