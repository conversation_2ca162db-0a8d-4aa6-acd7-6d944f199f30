import apiClient from "@/lib/api/apiClient"
import type {
  ListNftResponse,
  NftsOfAddressResponse,
  NftTokenDetailResponse,
  NftTransferResponse,
  StatsTopCollectionMetric,
  StatsTopMintCollectionMetric,
} from "@/types/nft"
import { useQuery } from "@tanstack/react-query"

export const useGetContractsNftByPageAndSize = (
  page: number,
  limit: number,
) => {
  return useQuery({
    queryKey: ["nfts", page, limit],
    queryFn: async () => {
      const response = await apiClient.post<ListNftResponse>("api/", {
        jsonrpc: "2.0",
        method: "eth_getContractsNftByPageAndSize",
        params: [page, limit],
        id: 1,
      })
      return response?.result
    },
  })
}

export const useGetStatsTopCollections = (
  metric: StatsTopCollectionMetric,
  page: number,
  limit: number,
) => {
  return useQuery({
    queryKey: ["stats_top_collections", metric, page, limit],
    queryFn: async () => {
      const response = await apiClient.post<ListNftResponse["result"]>("api/", {
        jsonrpc: "2.0",
        method: "stats_top_collections",
        params: [metric, page, limit],
        id: 1,
      })
      return response?.result
    },
  })
}

export const useGetTopMintCollections = (
  metric: StatsTopMintCollectionMetric,
  page: number,
  limit: number,
) => {
  return useQuery({
    queryKey: ["stats_top_mint_collections", metric, page, limit],
    queryFn: async () => {
      const response = await apiClient.post<ListNftResponse["result"]>("api/", {
        jsonrpc: "2.0",
        method: "stats_top_mint_collections",
        params: [metric, page, limit],
        id: 1,
      })
      return response?.result
    },
  })
}

export const useGetNftTokenDetail = (tokenAddress: string, tokenId: string) => {
  return useQuery({
    queryKey: ["nft", "token", tokenAddress, tokenId],
    queryFn: async () => {
      if (!tokenAddress || !tokenId) return undefined

      const response = await apiClient.post<NftTokenDetailResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getNftTokenDetail",
          params: [tokenAddress, tokenId],
          id: 1,
        },
      )

      return response?.result
    },
    enabled: !!(tokenAddress && tokenId),
  })
}

export const useGetNftsOfAddress = (address: string, page = 0, size = 25) => {
  return useQuery({
    queryKey: ["nfts", "address", address, page, size],
    queryFn: async () => {
      if (!address) return undefined

      const response = await apiClient.post<NftsOfAddressResponse>("api/", {
        jsonrpc: "2.0",
        method: "eth_getNftsOfAddress",
        params: [address, page, size],
        id: 1,
      })

      return response?.result
    },
    enabled: !!address,
  })
}

export const useGetNftTransfer = (
  tokenAddress: string | null,
  page: number,
  limit: number,
) => {
  return useQuery({
    queryKey: ["nfts-transfer", page, limit],
    queryFn: async () => {
      const response = await apiClient.post<NftTransferResponse["result"]>(
        "api/",
        {
          jsonrpc: "2.0",
          method: "eth_getTransferNft",
          params: [tokenAddress, page, limit],
          id: 1,
        },
      )
      return response?.result
    },
  })
}
