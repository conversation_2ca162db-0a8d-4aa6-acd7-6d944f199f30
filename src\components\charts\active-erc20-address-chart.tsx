"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { But<PERSON> } from "@/components/ui/button"
import { ChartTypeEnum, getTimeRanges, use<PERSON>hart } from "@/hooks/useChart"
import { Download, Info } from "lucide-react"
import { useMemo, useState } from "react"
import { Line, LineChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { downloadCSV, generateFilename, CHART_COLUMNS } from "@/utils/csv-export"
import TimeRangeSelector from "./time-range-seletor"

interface ActiveERC20AddressTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
}

const ActiveERC20AddressTooltip = ({ active, payload, label }: ActiveERC20AddressTooltipProps) => {
  if (!active || !payload || !payload.length) return null

  const data = payload[0].payload
  // Format the date safely
  let formattedDate = ""
  try {
    if (label) {
      const date = new Date(label)
      formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    }
  } catch (e) {
    console.error("Error formatting date:", e)
    formattedDate = String(label)
  }

  return (
    <div className="rounded-lg border bg-background/95 p-3 shadow-md">
      <div className="mb-2 flex items-center gap-2">
        <div className="h-3 w-3 rounded-full bg-primary" />
        <span className="font-medium">{formattedDate}</span>
      </div>
      <div className="space-y-1 text-sm">
        <p>
          <span className="text-muted-foreground">Active ERC20 Addresses: </span>
          <span className="font-medium">{data.erc20AddressActive.toLocaleString()}</span>
        </p>
        <p>
          <span className="text-muted-foreground">Receive from: </span>
          <span className="font-medium">{data.erc20AddressReceive.toLocaleString()}</span>
        </p>
        <p>
          <span className="text-muted-foreground">Send to: </span>
          <span className="font-medium">{data.erc20AddressSend.toLocaleString()}</span>
        </p>
      </div>
    </div>
  )
}

export default function ActiveERC20AddressChart() {
  const [timeRange, setTimeRange] = useState<string>("1m")
  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "1m":
        return getTimeRanges("30d")
      case "6m":
        return getTimeRanges("180d")
      case "1y":
        return getTimeRanges("1y")
      case "all":
        return getTimeRanges("all")
      default:
        return getTimeRanges("30d")
    }
  }, [timeRange])

  const { data, isLoading, error } = useChart(startTime, endTime, ChartTypeEnum.ACTIVE_ERC20_ADDRESS)

  // Format data for charts - keep original field names
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return []

    return data.map((item: any) => ({
      date: new Date(item.timestamp).toISOString().split("T")[0],
      timestamp: item.timestamp,
      // Keep the original field names
      erc20AddressActive: item.erc20AddressActive || 0,
      erc20AddressSend: item.erc20AddressSend || 0,
      erc20AddressReceive: item.erc20AddressReceive || 0,
    }))
  }, [data])

  // Handle CSV download
  const handleDownloadCSV = () => {
    if (!chartData.length) return

    downloadCSV({
      filename: generateFilename('active-erc20-addresses', timeRange),
      data: chartData,
      columns: CHART_COLUMNS.ERC20_ADDRESS,
    })
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Active ERC20 Addresses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-[300px] items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card className="bg-card/50">
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Active ERC20 Addresses</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <TimeRangeSelector timeRange={timeRange} onChange={setTimeRange} />
        </CardHeader>
        <CardContent>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex h-full w-full items-center justify-center">
                <Skeleton className="h-[450px] w-full" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => {
                      const date = new Date(value)
                      return `${date.getMonth() + 1}/${date.getDate()}`
                    }}
                  />
                  <YAxis
                    tickFormatter={(value) => {
                      if (value >= 1000) {
                        return `${(value / 1000).toFixed(0)}k`
                      }
                      return value.toString()
                    }}
                    label={{
                      value: "Active ERC20 Addresses",
                      angle: -90,
                      position: "insideLeft",
                      style: { textAnchor: "middle" },
                    }}
                  />
                  <Tooltip content={<ActiveERC20AddressTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="erc20AddressActive"
                    name="Active ERC20 Addresses"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 6, fill: "hsl(var(--primary))" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleDownloadCSV}
              disabled={chartData.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Active ERC20 Address chart shows the daily number of unique addresses that were active in ERC20 token
            transfers on the network as a sender or receiver. This chart provides insights into token usage and adoption
            trends.
          </p>
          <p className="mt-2 text-sm text-muted-foreground">
            Click and drag in the plot area to zoom in. You can also use the time range buttons to view different time
            periods.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

