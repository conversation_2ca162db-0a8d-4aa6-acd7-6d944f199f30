"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ChartType,
  getDataFieldForChartType,
  getTimeRanges,
  useChart,
} from "@/hooks/useChart";
import { Download, Info } from "lucide-react";
import { useMemo, useState } from "react";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { downloadCSV, generateFilename, CHART_COLUMNS } from "@/utils/csv-export";
import EnhancedTooltip from "./enhanced-tooltip";

interface TransactionsDetailProps {
  chartType: ChartType;
  color: string;
}

export default function TransactionsDetail({
  chartType,
  color,
}: TransactionsDetailProps) {
  const [timeRange, setTimeRange] = useState<"7d" | "1m" | "3m" | "all">("1m");
  const { startTime, endTime } = useMemo(() => {
    switch (timeRange) {
      case "7d":
        return getTimeRanges("7d");
      case "1m":
        return getTimeRanges("30d");
      case "3m":
        return getTimeRanges("90d");
      case "all":
        return getTimeRanges("all");
      default:
        return getTimeRanges("30d");
    }
  }, [timeRange]);

  console.log('1111111111')

  const { data, isLoading, error } = useChart(startTime, endTime, chartType);
  const dataField = getDataFieldForChartType(chartType);

  // Format data for charts
  const chartData = useMemo(() => {
    if (!data || !Array.isArray(data)) return [];

    return data.map((item: any) => {
      const value = item[dataField];
      return {
        date: new Date(item.timestamp).toISOString().split("T")[0],
        value: typeof value === "string" ? Number.parseFloat(value) : value,
        ...item,
      };
    });
  }, [data, dataField]);

  // Get the latest data point for stats
  const latestData = useMemo(() => {
    if (!chartData.length) return null;
    return chartData[chartData.length - 1];
  }, [chartData]);

  // Handle CSV download
  const handleDownloadCSV = () => {
    if (!chartData.length) return;

    // Define columns based on available data
    const columns = [
      { key: 'value', header: 'Transaction Count', formatter: (v: number) => v.toLocaleString() },
      { key: 'avgBlockTime', header: 'Avg Block Time (s)', formatter: (v: number) => v?.toFixed(2) || '' },
      { key: 'avgBlockSize', header: 'Avg Block Size (bytes)', formatter: (v: string) => Number(v)?.toLocaleString() || '' },
      { key: 'blockCount', header: 'Block Count', formatter: (v: number) => v?.toLocaleString() || '' },
      { key: 'newAddressSeen', header: 'New Addresses', formatter: (v: number) => v?.toLocaleString() || '' },
    ];

    downloadCSV({
      filename: generateFilename('transactions-detail', timeRange),
      data: chartData,
      columns,
    });
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Daily Transactions Chart</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-destructive">Error loading chart data</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle>Helios Daily Transactions Chart</CardTitle>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Info className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex space-x-1">
              <Button
                variant={timeRange === "7d" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("7d")}
              >
                7d
              </Button>
              <Button
                variant={timeRange === "1m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("1m")}
              >
                1m
              </Button>
              <Button
                variant={timeRange === "3m" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("3m")}
              >
                3m
              </Button>
              <Button
                variant={timeRange === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setTimeRange("all")}
              >
                All
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            {isLoading ? (
              <div className="w-full h-full flex items-center justify-center">
                <Skeleton className="w-full h-[350px]" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={chartData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient
                      id="colorTransactions"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                      <stop offset="95%" stopColor={color} stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return `${date.getMonth() + 1}/${date.getDate()}`;
                    }}
                  />
                  <YAxis tickFormatter={(value) => value.toLocaleString()} />
                  <Tooltip
                    content={<EnhancedTooltip chartType={chartType} />}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke={color}
                    fillOpacity={1}
                    fill="url(#colorTransactions)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </div>
          <div className="mt-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center"
              onClick={handleDownloadCSV}
              disabled={chartData.length === 0}
            >
              <Download className="mr-2 h-4 w-4" />
              Download CSV Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {latestData && (
        <Card>
          <CardHeader>
            <CardTitle>Latest Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Metric</TableHead>
                  <TableHead>Value</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>Transactions</TableCell>
                  <TableCell>
                    {latestData.value?.toLocaleString() || "N/A"}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Avg Block Time</TableCell>
                  <TableCell>
                    {latestData.avgBlockTime?.toFixed(2) || "N/A"} seconds
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Avg Block Size</TableCell>
                  <TableCell>
                    {Number(latestData.avgBlockSize)?.toLocaleString() || "N/A"}{" "}
                    bytes
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Block Count</TableCell>
                  <TableCell>
                    {latestData.blockCount?.toLocaleString() || "N/A"}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>New Addresses</TableCell>
                  <TableCell>
                    {latestData.newAddressSeen?.toLocaleString() || "N/A"}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>About</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            The Helios Daily Transactions Chart shows the number of transactions
            processed on the Helios blockchain each day. This is a key metric
            for measuring network activity and adoption.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
