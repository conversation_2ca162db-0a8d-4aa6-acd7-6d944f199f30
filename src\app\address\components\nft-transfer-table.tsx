"use client"

import React from "react"
import { use<PERSON>outer, useSearchParams } from "next/navigation"
import { useGetNftTransfer } from "@/hooks/useNft"
import { CSVExportButton } from "@/components/csv-button-export"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { PaginationWithLinks } from "@/components/ui/pagination-with-links"
import { PATH_ROUTER } from "@/constants/routers"
import { formatHash, formatTimestamp, normalizeAddress } from "@/helpers/format"
import CopyTooltip from "@/app/components/copy-tooltip"
import type { DaumNftTransfer } from "@/types/nft"
import { NFTI<PERSON>, To<PERSON><PERSON><PERSON> } from "@/components/nft-image"
import { AlertCircle, ImageIcon } from "lucide-react"

interface NFTTransfersTableProps {
  address: string
  showExport?: boolean
  exportFilename?: string
  showPagination?: boolean
}

export function NFTTransfersTable({
  address,
  showExport = false,
  exportFilename = "nft-transfers.csv",
  showPagination = true,
}: NFTTransfersTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Number(searchParams.get("page")) || 1
  const pageSize = Number(searchParams.get("pageSize")) || 25

  const { data, isLoading, isError, error } = useGetNftTransfer(
    address,
    page,
    pageSize,
  )

  // Format data for CSV export
  const formatNftTransfersForCSV = (transfers: DaumNftTransfer[]) => {
    return transfers.map((transfer) => ({
      "Transaction Hash": transfer.transactionHash,
      Method: transfer.functionSignature || "Transfer",
      Block: transfer.blockNumber,
      "Date(UTC)": new Date(transfer.timestamp).toISOString().split("T")[0],
      UnixTimeStamp: Math.floor(new Date(transfer.timestamp).getTime() / 1000),
      Age: formatTimestamp(transfer.timestamp),
      From: normalizeAddress(transfer.from),
      To: normalizeAddress(transfer.to),
      "Token ID": transfer.tokenId,
      "Token Address": normalizeAddress(transfer.tokenAddress),
      "Token Name": transfer.tokenDetails?.name || "Unknown",
      "Token Symbol": transfer.tokenDetails?.symbol || "Unknown",
      "NFT Name": transfer.nftDetails?.standard || "Unknown",
      Amount: transfer.amount,
      Value: transfer.value,
    }))
  }

  const handleNftClick = (transfer: DaumNftTransfer) => {
    if (transfer.tokenAddress && transfer.tokenId) {
      router.push(`/nft/${transfer.tokenAddress}/${transfer.tokenId}`)
    }
  }

  const renderTokenLogo = (tokenDetails: DaumNftTransfer["tokenDetails"]) => {
    return (
      <TokenLogo
        logoHash={tokenDetails?.logo}
        tokenName={tokenDetails?.name}
        size="sm"
      />
    )
  }

  const renderNftImage = (nftDetails: DaumNftTransfer["nftDetails"]) => {
    return (
      <NFTImage
        src={nftDetails?.image}
        alt={`NFT #${nftDetails?.tokenId || "Unknown"}`}
        size="sm"
        rounded={false}
      />
    )
  }

  if (isError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load NFT transfers: {error?.message || "Unknown error"}
        </AlertDescription>
      </Alert>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <Skeleton className="h-12 w-12 rounded" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-[200px]" />
              <Skeleton className="h-4 w-[150px]" />
            </div>
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-4 w-[100px]" />
          </div>
        ))}
      </div>
    )
  }

  const transfers = data?.data || []

  return (
    <div>
      {showExport && transfers.length > 0 && (
        <div className="flex justify-end m-4">
          <CSVExportButton
            data={transfers}
            formatter={formatNftTransfersForCSV}
            filename={exportFilename}
          />
        </div>
      )}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Transaction Hash</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Block</TableHead>
              <TableHead>Age</TableHead>
              <TableHead>From</TableHead>
              <TableHead>To</TableHead>
              <TableHead>Token ID</TableHead>
              <TableHead>NFT</TableHead>
              <TableHead>Collection</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transfers.length > 0 ? (
              transfers.map((transfer, index) => (
              <TableRow key={`${transfer.transactionHash}-${index}`}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <CopyTooltip content={transfer.transactionHash} />
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            router.push(
                              PATH_ROUTER.TRANSACTION_DETAIL(
                                transfer.transactionHash,
                              ),
                            )
                          }}
                        >
                          {formatHash(transfer.transactionHash, 10, 8)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {transfer.transactionHash}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {transfer.functionSignature || "Transfer"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline cursor-pointer"
                        onClick={() => {
                          router.push(
                            PATH_ROUTER.BLOCK_DETAIL(
                              transfer.blockNumber.toString(),
                            ),
                          )
                        }}
                      >
                        {transfer.blockNumber}
                      </TooltipTrigger>
                      <TooltipContent>
                        Block #{transfer.blockNumber}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        {formatTimestamp(transfer.timestamp)}
                      </TooltipTrigger>
                      <TooltipContent>
                        {new Date(transfer.timestamp).toLocaleString()}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            const address = normalizeAddress(transfer.from)
                            if (address) {
                              router.push(PATH_ROUTER.ADDRESS_DETAIL(address))
                            }
                          }}
                        >
                          {formatHash(normalizeAddress(transfer.from), 6, 4)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(transfer.from)}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <CopyTooltip
                      content={
                        normalizeAddress(transfer.from) || transfer.from
                      }
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger
                          className="text-blue-700 hover:underline cursor-pointer"
                          onClick={() => {
                            const address = normalizeAddress(transfer.to)
                            if (address) {
                              router.push(PATH_ROUTER.ADDRESS_DETAIL(address))
                            }
                          }}
                        >
                          {formatHash(normalizeAddress(transfer.to), 6, 4)}
                        </TooltipTrigger>
                        <TooltipContent>
                          {normalizeAddress(transfer.to)}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <CopyTooltip
                      content={normalizeAddress(transfer.to) || transfer.to}
                    />
                  </div>
                </TableCell>
                <TableCell>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger
                        className="text-blue-700 hover:underline cursor-pointer font-mono"
                        onClick={() => handleNftClick(transfer)}
                      >
                        #{transfer.tokenId}
                      </TooltipTrigger>
                      <TooltipContent>
                        Token ID: {transfer.tokenId}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div
                    className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-1 rounded"
                    onClick={() => handleNftClick(transfer)}
                  >
                    {renderNftImage(transfer.nftDetails)}
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">
                        NFT #{transfer.tokenId}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {transfer.nftDetails?.standard || "Unknown"}
                      </span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {renderTokenLogo(transfer.tokenDetails)}
                    <div className="flex flex-col">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger
                            className="text-blue-700 hover:underline text-left cursor-pointer"
                            onClick={() => {
                              if (transfer.tokenDetails?.address) {
                                router.push(
                                  PATH_ROUTER.TOKEN_DETAIL(
                                    transfer.tokenDetails.address,
                                  ),
                                )
                              }
                            }}
                          >
                            <span className="text-sm font-medium">
                              {transfer.tokenDetails?.name || "Unknown"}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {transfer.tokenDetails?.name || "Unknown Token"}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                      <span className="text-xs text-muted-foreground">
                        {transfer.tokenDetails?.symbol || "Unknown"}
                      </span>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <ImageIcon className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">
                      No NFT transfers found
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {showPagination && data?.metadata && data.metadata.totalPages > 1 && (
        <div className="mt-6">
          <PaginationWithLinks
            page={page}
            pageSize={pageSize}
            totalCount={data.metadata.total}
            pageSizeSelectOptions={{
              pageSizeOptions: [10, 25, 50, 100],
            }}
          />
        </div>
      )}
    </div>
  )
}
