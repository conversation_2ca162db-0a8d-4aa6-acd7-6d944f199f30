"use client"

import Image from "next/image"
import { ImageIcon } from "lucide-react"
import { processNftImageUrl, processTokenLogoUrl, getNftFallbackImage } from "@/utils/nft-image"
import { cn } from "@/lib/utils"

interface NFTImageProps {
  src?: string | null
  alt: string
  className?: string
  size?: "sm" | "md" | "lg" | "xl" | "2xl"
  rounded?: boolean
  fallbackIcon?: React.ReactNode
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-12 w-12",
  lg: "h-16 w-16",
  xl: "h-24 w-24",
  "2xl": "h-32 w-32"
}

export function NFTImage({ 
  src, 
  alt, 
  className,
  size = "sm",
  rounded = false,
  fallbackIcon
}: NFTImageProps) {
  const { src: processedSrc, shouldOptimize } = processNftImageUrl(src)
  
  if (!src) {
    return (
      <div className={cn(
        sizeClasses[size],
        rounded ? "rounded-full" : "rounded",
        "bg-muted flex items-center justify-center border",
        className
      )}>
        {fallbackIcon || <ImageIcon className="h-4 w-4 text-muted-foreground" />}
      </div>
    )
  }

  return (
    <div className={cn(
      "relative overflow-hidden border",
      sizeClasses[size],
      rounded ? "rounded-full" : "rounded",
      className
    )}>
      <Image
        src={processedSrc}
        alt={alt}
        fill
        className="object-cover"
        unoptimized={!shouldOptimize}
        onError={(e) => {
          e.currentTarget.src = getNftFallbackImage()
        }}
      />
    </div>
  )
}

interface TokenLogoProps {
  logoHash?: string | null
  logo?: string | null 
  tokenName?: string
  className?: string
  size?: "sm" | "md" | "lg" | "xl" | "2xl"
}

export function TokenLogo({
  logoHash,
  logo,
  tokenName = "Token",
  className,
  size = "sm"
}: TokenLogoProps) {
  // Use logo prop if provided, otherwise use logoHash
  const logoValue = logo || logoHash
  const { src, shouldOptimize } = processTokenLogoUrl(logoValue)
  
  if (!logoValue) {
    return (
      <div className={cn(
        sizeClasses[size],
        "rounded-full bg-muted flex items-center justify-center border",
        className
      )}>
        <ImageIcon className="h-4 w-4 text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className={cn(
      "relative rounded-full overflow-hidden border",
      sizeClasses[size],
      className
    )}>
      <Image
        src={src}
        alt={`${tokenName} logo`}
        fill
        className="object-cover"
        unoptimized={!shouldOptimize}
        onError={(e) => {
          e.currentTarget.src = getNftFallbackImage()
        }}
      />
    </div>
  )
}
