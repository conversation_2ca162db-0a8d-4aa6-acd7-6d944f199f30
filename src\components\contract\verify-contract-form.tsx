"use client"

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card"
import { useState } from "react"
import { toast } from "react-toastify"

import { ContractCodeForm } from "@/components/contract/contract-code-form"
import { ContractDetailsForm } from "@/components/contract/contract-details-form"
import { ProgressSteps } from "@/components/contract/progress-steps"

import {
  useSolidityVerifierMultiplePart,
  useSolidityVerifierStandard,
  useVyperVerifierMultiplePart,
  useVyperVerifierStandard,
} from "@/hooks/useVerifyContract"

import { envs } from "@/constants/envs"
import type {
  ContractCodeFormValues,
  ContractDetailsFormValues,
  VerifyContractFormValues,
} from "@/lib/validations/contract-schema"
import {
  BytecodeType,
  CompilerType,
  VerifyResponseStatus,
} from "@/types/verify-contract"

export default function VerifyContractForm() {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<Partial<VerifyContractFormValues>>({
    contractAddress: "",
    compilerType: "",
    compilerVersion: "",
    licenseType: "",
    agreeToTerms: false,
    contractCode: "",
    optimization: "No",
    runs: "200",
    evmVersion: "default",
    constructorArguments: "",
    compilerVersionType: "",
    sourceFiles: {},
    bytecode: "",
  })
  const [isVerified, setIsVerified] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const steps = [
    { id: 1, label: "Enter Contract Details" },
    { id: 2, label: "Verify & Publish" },
  ]

  const { mutateAsync: verifySolidityContractStandard } =
    useSolidityVerifierStandard()
  const { mutateAsync: verifySolidityContractMultiplePart } =
    useSolidityVerifierMultiplePart()
  const { mutateAsync: verifyVyperContractStandard } =
    useVyperVerifierStandard()
  const { mutateAsync: verifyVyperContractMultiplePart } =
    useVyperVerifierMultiplePart()

  const handleDetailsSubmit = (data: ContractDetailsFormValues) => {
    setFormData({
      ...formData,
      ...data,
    })
    setStep(2)
  }

  const handleCodeSubmit = async (data: ContractCodeFormValues) => {
    try {
      setIsSubmitting(true)

      const completeFormData = {
        ...formData,
        ...data,
      } as VerifyContractFormValues

      console.log("Form submitted:", completeFormData)

      const metadata = {
        chainId: envs.HELIOS_CHAIN_ID,
        contractAddress: completeFormData.contractAddress,
        transactionHash:
          "******************************************000000000000000000000000",
        blockNumber: "0",
        transactionIndex: "0",
        deployer: "******************************************",
        creationCode: "",
        runtimeCode: "",
      }

      // Ensure we have bytecode
      if (!completeFormData.bytecode) {
        // Fetch bytecode if not already provided
        try {
          const response = await fetch("/api", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              jsonrpc: "2.0",
              method: "eth_getCode",
              params: [completeFormData.contractAddress, "latest"],
              id: 1,
            }),
          })

          const result = await response.json()
          if (result.result) {
            completeFormData.bytecode = result.result
          } else {
            throw new Error("Failed to fetch bytecode")
          }
        } catch (error) {
          console.error("Error fetching bytecode:", error)
          toast.error("Failed to fetch bytecode. Using default empty bytecode.")
          completeFormData.bytecode = "0x"
        }
      }

      let response
      if (completeFormData.compilerVersionType === "vyper") {
        if (completeFormData.compilerType === CompilerType.VYPER_JSON) {
          // For Vyper JSON input
          if (!Object.keys(completeFormData.sourceFiles || {}).length) {
            toast.error("Please upload a JSON file")
            return
          }

          const jsonFileName = Object.keys(
            completeFormData.sourceFiles || {},
          )[0]
          const jsonContent = completeFormData.sourceFiles?.[jsonFileName] || ""

          response = await verifyVyperContractStandard({
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            input: jsonContent,
            metadata,
          })
        } else if (completeFormData.compilerType === CompilerType.VYPER_MULTI) {
          // For Vyper multi-part files
          if (!Object.keys(completeFormData.sourceFiles || {}).length) {
            toast.error("Please upload Vyper files")
            return
          }

          response = await verifyVyperContractMultiplePart({
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            evmVersion: completeFormData.evmVersion,
            sourceFiles: completeFormData.sourceFiles || {
              "contract.vy": completeFormData.contractCode,
            },
            interfaces: {},
            metadata,
          })
        }
      } else {
        // Solidity verification
        if (completeFormData.compilerType === CompilerType.SOLIDITY_JSON) {
          // For Solidity JSON input
          if (!Object.keys(completeFormData.sourceFiles || {}).length) {
            toast.error("Please upload a JSON file")
            return
          }
          console.log(
            "completeFormData.sourceFiles",
            completeFormData.sourceFiles,
          )
          const jsonFileName = Object.keys(
            completeFormData.sourceFiles || {},
          )[0]
          console.log("jsonFileName", jsonFileName)
          const jsonContent = completeFormData.sourceFiles?.[jsonFileName] || ""

          console.log("metadata", metadata)

          console.log("params", {
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            input: jsonContent,
            // metadata,
            contractAddress: completeFormData.contractAddress,
          })
          response = await verifySolidityContractStandard({
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            input: jsonContent,
            contractAddress: completeFormData.contractAddress,
            // metadata,
          })
        } else if (
          completeFormData.compilerType === CompilerType.SOLIDITY_MULTI
        ) {
          // For Solidity multi-part files
          if (!Object.keys(completeFormData.sourceFiles || {}).length) {
            toast.error("Please upload Solidity files")
            return
          }

          // Validate that we have at least 2 files for multi-part
          if (Object.keys(completeFormData.sourceFiles || {}).length < 2) {
            toast.error("Multi-part verification requires at least 2 files")
            return
          }

          response = await verifySolidityContractMultiplePart({
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            evmVersion: completeFormData.evmVersion,
            optimizationRuns: completeFormData.runs,
            sourceFiles: completeFormData.sourceFiles || {
              "contract.sol": completeFormData.contractCode,
            },
            libraries: {},
            metadata,
          })
        } else {
          // Single file solidity (fallback)
          response = await verifySolidityContractMultiplePart({
            bytecode: completeFormData.bytecode || "",
            bytecodeType: BytecodeType.DEPLOYED_BYTECODE,
            compilerVersion: completeFormData.compilerVersion,
            evmVersion: completeFormData.evmVersion,
            optimizationRuns: completeFormData.runs,
            sourceFiles: { "contract.sol": completeFormData.contractCode },
            libraries: {},
            metadata,
          })
        }
      }

      console.log("Verification response:", response)

      if (response?.result?.status === VerifyResponseStatus.SUCCESS) {
        setIsVerified(true)
        toast.success("Contract verified successfully!")
      } else {
        toast.error(
          "Failed to verify contract. Please check your inputs and try again.",
        )
      }
    } catch (error) {
      console.error("Verification error:", error)
      toast.error(
        "Failed to verify contract. Please check your inputs and try again.",
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleReset = () => {
    setFormData({
      contractAddress: "",
      compilerType: "",
      compilerVersion: "",
      licenseType: "",
      agreeToTerms: false,
      contractCode: "",
      optimization: "No",
      runs: "200",
      evmVersion: "default",
      constructorArguments: "",
      compilerVersionType: "",
      sourceFiles: {},
      bytecode: "",
    })
    setStep(1)
    setIsVerified(false)
  }

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h1 className="text-2xl md:text-3xl font-bold">
          Verify & Publish Contract Source Code
        </h1>
        <p className="text-sm md:text-base mt-2">
          Source code verification provides transparency for users interacting
          with smart contracts. By uploading the source code, Helios will match
          the compiled code with that on the blockchain.{" "}
          {/* <a href="#" className="text-primary hover:underline">
            Read more.
          </a> */}
        </p>
        <p className="text-sm mt-2">
          A simple and structured interface for verifying source contracts that
          fit in a single file.
        </p>
      </div>

      <ProgressSteps currentStep={step} steps={steps} setStep={setStep} />

      <div className="card">
        <Card>
          <CardHeader>
            <CardTitle>
              {step === 1
                ? "Enter Contract Details"
                : "Upload Contract Source Code"}
            </CardTitle>
            {step === 2 && (
              <CardDescription>
                Please follow the instructions below to verify your contract
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            {step === 1 ? (
              <ContractDetailsForm
                onSubmit={handleDetailsSubmit}
                defaultValues={formData}
              />
            ) : (
              <ContractCodeForm
                onSubmit={handleCodeSubmit}
                onReset={handleReset}
                contractDetails={{
                  contractAddress: formData.contractAddress || "",
                  compilerType: formData.compilerType || "",
                  compilerVersion: formData.compilerVersion || "",
                }}
                isVerified={isVerified}
                isSubmitting={isSubmitting}
                defaultValues={formData}
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
